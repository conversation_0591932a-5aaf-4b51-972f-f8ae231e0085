package main

import (
	"context"
	"fmt"
	"strings"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var MLSMergedTable = map[string]string{
	"DDF": "mls_crea_ddf_records",
	"TRB": "mls_treb_master_records",
	"OTW": "mls_oreb_master_records",
	"CLG": "mls_creb_master_records",
}

// needToProcess determines whether a property needs to be processed based on disk and migration stage
func needToProcess(prop bson.M, disk string) bool {
	src, ok := prop["src"].(string)
	if !ok {
		return false
	}

	// For ca6 stage: Skip if already processed (has phoLH field)
	if disk == "ca6" {
		if prop["phoLH"] != nil {
			return false
		}
	}

	// For ca7 stage: Check if ca7 processing is already done by looking for _old fields
	if disk == "ca7" {
		switch src {
		case SRC_TRB, SRC_DDF:
			if prop["pho_old"] != nil {
				return false
			}
		case SRC_OTW, SRC_CLG:
			if prop["phoIDs_old"] != nil {
				return false
			}
		}
		return true
	}

	// Common logic for both ca6 and ca7: check original fields
	// For TRB and DDF, check pho field
	if src == SRC_TRB || src == SRC_DDF {
		// Process when: has pho > 0 AND no thumbUrl (or thumbUrl is empty)
		hasPho := getIntValue(prop, "pho") > 0
		// Check if thumbUrl is nil or empty string
		thumbUrl := prop["thumbUrl"]
		hasNoThumbUrl := thumbUrl == nil || thumbUrl == ""
		return hasPho && hasNoThumbUrl
	}

	// For OTW and CLG, check phoIDs field
	if src == SRC_OTW || src == SRC_CLG {
		return hasNonEmptyArray(prop, "phoIDs")
	}

	return false
}

// getIntValue safely extracts integer value from bson.M field, handling various MongoDB integer types
func getIntValue(prop bson.M, key string) int {
	value := prop[key]
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case float64:
		return int(v) // MongoDB sometimes returns float64 for integer values
	default:
		golog.Warn("Unexpected type for integer field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
		return 0
	}
}

// hasNonEmptyArray checks if the specified field contains a non-empty array, handling various MongoDB array types
func hasNonEmptyArray(prop bson.M, key string) bool {
	value := prop[key]
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case []interface{}:
		return len(v) > 0
	case []string:
		return len(v) > 0
	case []int:
		return len(v) > 0
	case bson.A: // MongoDB BSON array type
		return len(v) > 0
	case []int32:
		return len(v) > 0
	case []int64:
		return len(v) > 0
	default:
		golog.Warn("Unexpected type for array field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
		return false
	}
}

// getPropertiesCursor gets a cursor for streaming properties that need to be processed
func getPropertiesCursor(config Config) (*mongo.Cursor, error) {
	collection := gomongo.Coll("vow", "properties")
	if collection == nil {
		return nil, fmt.Errorf("failed to get properties collection")
	}

	// Build query filter
	filter := bson.M{}
	if config.MT != nil {
		filter["mt"] = bson.M{"$gte": *config.MT}
	}

	// Build projection to include necessary fields
	projection := bson.M{
		"mt":       1,
		"sid":      1,
		"src":      1,
		"ts":       1,
		"board":    1,
		"pho":      1,
		"phoIDs":   1,
		"orgId":    1,
		"phoLH":    1,
		"onD":      1,
		"thumbUrl": 1,
	}

	// Build query options with reverse chronological order
	opts := options.Find().
		SetProjection(projection).
		SetSort(bson.M{"ts": -1})

	ctx := context.Background()
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to query properties: %w", err)
	}

	golog.Info("Created properties cursor for streaming")
	return cursor, nil
}

// insertMigrationLog inserts migration log into database
func insertMigrationLog(result MigrationResult) error {
	var collectionName string
	if result.Disk == "ca6" {
		collectionName = "mls_photo_migration_ca6"
	} else {
		collectionName = "mls_photo_migration_ca7"
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	ctx := context.Background()
	_, err := collection.InsertOne(ctx, result)
	if err != nil {
		return fmt.Errorf("failed to insert migration log: %w", err)
	}

	return nil
}

// updateDirStatsFromProp updates directory statistics from property data using phoP path
func updateDirStatsFromProp(prop bson.M, phoP string, fileCount int) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		return fmt.Errorf("invalid sid field")
	}

	// Get dirStore based on src type
	var dirStore *levelStore.DirKeyStore
	switch src {
	case SRC_TRB:
		dirStore = storeTRB
	case SRC_DDF:
		dirStore = storeDDF
	case SRC_OTW:
		dirStore = storeOTW
	case SRC_CLG:
		dirStore = storeCLG
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	if dirStore == nil {
		return fmt.Errorf("dirStore not initialized for src: %s", src)
	}

	// Parse L1/L2 from phoP path by splitting on "/"
	// phoP format: "/L1/L2" (e.g., "/100/abc123")
	pathParts := strings.Split(strings.TrimPrefix(phoP, "/"), "/")
	if len(pathParts) < 2 {
		return fmt.Errorf("invalid phoP path format: %s", phoP)
	}

	l1 := pathParts[0]
	l2 := pathParts[1]

	// Update stats: 1 property, fileCount files
	if fileCount != 0 {
		dirStore.AddDirStats(l1, l2, 1, fileCount) // 1 means one property
	}

	// Force l2 to be displayed as string by adding quotes (following the pattern)
	golog.Info("updated dirStore stats", "l1", l1, "l2", fmt.Sprintf("\"%s\"", l2), "fileCount", fileCount, "sid", sid, "phoP", phoP, "src", src)

	return nil
}

// updateDB updates both properties and RNI collections based on ca6/ca7 stage
func updateDB(params DBUpdateParams) error {
	propID, ok := params.Prop["_id"].(string)
	if !ok {
		return fmt.Errorf("invalid prop _id")
	}

	switch params.Disk {
	case "ca6":
		// ca6 stage: Add new phoLH, tnLH, and phoP fields
		if err := updatePropDBCA6(propID, params.PhoLH, params.TnLH, params.PhoP); err != nil {
			return fmt.Errorf("failed to update prop DB for ca6: %w", err)
		}

		// Update RNI collection
		if err := updateRniDBCA6(params.Prop, params.PhoLH, params.TnLH, params.PhoP); err != nil {
			return fmt.Errorf("failed to update rni DB: %w", err)
		}

		// Update directory statistics (only for ca6)
		if err := updateDirStatsFromProp(params.Prop, params.PhoP, len(params.PhoLH)); err != nil {
			golog.Error("Failed to update directory stats", "error", err)
			// Directory stats failure doesn't affect main process
		}
	case "ca7":
		// ca7 stage: Rename old fields to _old
		if err := updatePropDBCA7(propID, params.Prop); err != nil {
			return fmt.Errorf("failed to update prop DB for ca7: %w", err)
		}

		// Update RNI collection for ca7 (rename old fields)
		if err := updateRniDBCA7(params.Prop); err != nil {
			return fmt.Errorf("failed to update rni DB for ca7: %w", err)
		}
	default:
		return fmt.Errorf("unsupported disk: %s", params.Disk)
	}

	return nil
}

// updatePropDBCA6 updates both properties and merged collections for ca6 stage
func updatePropDBCA6(propID string, phoLH []int32, tnLH int32, phoP string) error {
	updateFields := bson.M{
		"phoLH": phoLH,
		"phoP":  phoP,
	}

	// Only include tnLH if it's not 0 (meaning thumbnail was created successfully)
	if tnLH != 0 {
		updateFields["tnLH"] = tnLH
	}

	update := bson.M{"$set": updateFields}
	return updatePropCollection(propID, update, "ca6")
}

// updateRniDBCA6 updates the corresponding RNI collection with phoP, phoLH, and tnLH
func updateRniDBCA6(prop bson.M, phoLH []int32, tnLH int32, phoP string) error {
	updateFields := bson.M{
		"phoLH": phoLH,
		"phoP":  phoP,
	}

	// Only include tnLH if it's not 0 (meaning thumbnail was created successfully)
	if tnLH != 0 {
		updateFields["tnLH"] = tnLH
	}

	update := bson.M{"$set": updateFields}
	return updateRniCollection(prop, update, "ca6")
}

// updatePropDBCA7 updates both properties and merged collections for ca7 stage (rename old fields)
func updatePropDBCA7(propID string, prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	update := bson.M{}

	// Rename fields based on source type using $rename operator
	switch src {
	case SRC_TRB, SRC_DDF:
		update["$rename"] = bson.M{"pho": "pho_old"}
	case SRC_OTW, SRC_CLG:
		update["$rename"] = bson.M{"phoIDs": "phoIDs_old"}
	}

	if len(update) == 0 {
		golog.Info("No fields to rename for ca7", "propID", propID, "src", src)
		return nil
	}

	// Update properties collection only
	return updatePropCollection(propID, update, "ca7")
}

// updateRniDBCA7 updates the RNI collection for ca7 stage (rename old fields)
func updateRniDBCA7(prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	update := bson.M{}

	// Rename fields based on source type
	switch src {
	case SRC_TRB:
		update["$rename"] = bson.M{"picNum": "picNum_old", "pic_num": "pic_num_old", "pho": "pho_old"}
	case SRC_DDF:
		update["$rename"] = bson.M{"picNum": "picNum_old"}
	case SRC_OTW, SRC_CLG:
		update["$rename"] = bson.M{"phoIDs": "phoIDs_old"}
	}

	return updateRniCollection(prop, update, "ca7")
}

// updateRniCollection is a common function to update RNI collections
func updateRniCollection(prop bson.M, update bson.M, stage string) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var collectionName string
	var queryField string
	var queryValue interface{}

	// Get collection name from BoardMergedTable
	var exists bool
	collectionName, exists = MLSMergedTable[src]
	if !exists {
		return fmt.Errorf("unsupported src type: %s", src)
	}

	// Determine query parameters based on source type
	switch src {
	case SRC_TRB, SRC_DDF:
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_CLG:
		queryField = "ListingId"
		queryValue = prop["sid"]
	case SRC_OTW:
		queryField = "sid"
		queryValue = prop["sid"]
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	filter := bson.M{queryField: queryValue}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update rni for %s: %w", stage, err)
	}

	if result.MatchedCount == 0 {
		golog.Info("No rni record found for update", "stage", stage, "collection", collectionName, "filter", filter)
	} else {
		golog.Info("Updated rni", "stage", stage, "collection", collectionName, "filter", filter, "update	", update)
	}

	return nil
}

// updatePropCollection is a common function to update properties collection
func updatePropCollection(propID string, update bson.M, stage string) error {
	ctx := context.Background()

	// Update properties collection
	propCollection := gomongo.Coll("vow", "properties")
	if propCollection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	filter := bson.M{"_id": propID}
	propResult, err := propCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update prop for %s: %w", stage, err)
	}

	if propResult.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	golog.Info("Updated prop", "stage", stage, "propID", propID, "update", update)

	return nil
}
