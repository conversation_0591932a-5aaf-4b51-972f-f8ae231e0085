#!/bin/bash

# Safe E2E Test Runner for Photo Migration
# This script provides a safe way to run destructive E2E tests

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🔒 Photo Migration E2E Test Safety Runner"
echo

# Check if we're in the right directory
if [[ ! -f "migration_e2e_test.go" ]]; then
    print_status $RED "❌ Error: Must run from photoMigration directory"
    exit 1
fi

# Safety confirmation
print_status $YELLOW "⚠️  WARNING: This will run DESTRUCTIVE tests that:"
echo "   • Drop database collections"
echo "   • Delete test files from file system"
echo "   • Should ONLY be run in safe test environments"
echo

read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    print_status $BLUE "✅ Aborted safely"
    exit 0
fi

# Environment setup
print_status $BLUE "🔧 Setting up safe test environment..."

# Create safe test directories
TEST_SOURCE_DIR="/tmp/safe_test_mlsimgs_$$"
TEST_TARGET_DIR="/tmp/safe_test_imgs_$$"

mkdir -p "$TEST_SOURCE_DIR"
mkdir -p "$TEST_TARGET_DIR"

print_status $GREEN "✅ Created safe test directories:"
echo "   Source: $TEST_SOURCE_DIR"
echo "   Target: $TEST_TARGET_DIR"

# Set environment variables
export ENABLE_DESTRUCTIVE_TEST_CLEANUP=true
export TEST_SOURCE_PATH="$TEST_SOURCE_DIR"
export TEST_TARGET_PATH="$TEST_TARGET_DIR"
export MONGODB_URI="${MONGODB_URI:-mongodb://localhost:27017/safe_test_db_$$}"

print_status $GREEN "✅ Environment variables set:"
echo "   ENABLE_DESTRUCTIVE_TEST_CLEANUP=true"
echo "   TEST_SOURCE_PATH=$TEST_SOURCE_PATH"
echo "   TEST_TARGET_PATH=$TEST_TARGET_PATH"
echo "   MONGODB_URI=$MONGODB_URI"
echo

# Run the tests
print_status $BLUE "🧪 Running E2E tests..."
echo

if go test -v -run "TestE2EMigrationRenameMode" -timeout 5m; then
    print_status $GREEN "✅ All tests passed!"
else
    print_status $RED "❌ Some tests failed"
    exit_code=1
fi

# Cleanup
print_status $BLUE "🧹 Cleaning up test directories..."
rm -rf "$TEST_SOURCE_DIR" "$TEST_TARGET_DIR"
print_status $GREEN "✅ Cleanup completed"

echo
if [[ ${exit_code:-0} -eq 0 ]]; then
    print_status $GREEN "🎉 Test run completed successfully!"
else
    print_status $RED "💥 Test run completed with failures"
    exit $exit_code
fi
