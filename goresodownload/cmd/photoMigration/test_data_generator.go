package main

import (
	"fmt"
	"math/rand"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

// TestProperty represents a test property for migration
type TestProperty struct {
	ID     string
	Src    string
	Sid    string
	OnD    int
	Pho    int
	PhoIDs []string
	Images []TestImage
	Board  string
	OrgId  string
}

// TestImage represents a test image file
type TestImage struct {
	RelativePath string
	Content      []byte
	Hash         int32
}

// TestDataGenerator generates test data for E2E tests
type TestDataGenerator struct {
	rand *rand.Rand
}

// NewTestDataGenerator creates a new test data generator
func NewTestDataGenerator() *TestDataGenerator {
	return &TestDataGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateTRBProperty generates a test TRB property
func (g *TestDataGenerator) GenerateTRBProperty(id, sid string, imageCount int) *TestProperty {
	onD := 20250101 + g.rand.Intn(365) // Random date in 2025

	images := make([]TestImage, imageCount)
	for i := 0; i < imageCount; i++ {
		var relativePath string
		if i == 0 {
			relativePath = fmt.Sprintf("treb/mls/%s/%s/%s.jpg",
				sid[len(sid)-1:], sid[len(sid)-3:], sid)
		} else {
			relativePath = fmt.Sprintf("treb/mls/%s/%s/%s_%d.jpg",
				sid[len(sid)-1:], sid[len(sid)-3:], sid, i)
		}

		images[i] = TestImage{
			RelativePath: relativePath,
		}
	}

	return &TestProperty{
		ID:     id,
		Src:    "TRB",
		Sid:    sid,
		OnD:    onD,
		Pho:    imageCount,
		Board:  "TRB",
		OrgId:  fmt.Sprintf("org_%d", g.rand.Intn(1000)),
		Images: images,
	}
}

// GenerateDDFProperty generates a test DDF property
func (g *TestDataGenerator) GenerateDDFProperty(id, sid string, imageCount int) *TestProperty {
	onD := 20250101 + g.rand.Intn(365) // Random date in 2025

	images := make([]TestImage, imageCount)
	for i := 0; i < imageCount; i++ {
		relativePath := fmt.Sprintf("crea/ddf/img/%s/%s_%d.jpg",
			sid[len(sid)-3:], sid, i+1)

		images[i] = TestImage{
			RelativePath: relativePath,
		}
	}

	return &TestProperty{
		ID:     id,
		Src:    "DDF",
		Sid:    sid,
		OnD:    onD,
		Pho:    imageCount,
		Board:  "DDF",
		OrgId:  fmt.Sprintf("org_%d", g.rand.Intn(1000)),
		Images: images,
	}
}

// GenerateOTWProperty generates a test OTW property
func (g *TestDataGenerator) GenerateOTWProperty(id, sid string, imageCount int) *TestProperty {
	onD := 20250101 + g.rand.Intn(365) // Random date in 2025

	phoIDs := make([]string, imageCount)
	images := make([]TestImage, imageCount)

	for i := 0; i < imageCount; i++ {
		phoIDs[i] = fmt.Sprintf("%d", i)

		// Calculate mui_last34 and mui_last12 from sid
		mui_last34 := sid[len(sid)-2:]
		mui_last12 := sid[len(sid)-2:]

		relativePath := fmt.Sprintf("oreb/mls/%s/%s/%s_%d.jpg",
			mui_last34, mui_last12, sid, i)

		images[i] = TestImage{
			RelativePath: relativePath,
		}
	}

	return &TestProperty{
		ID:     id,
		Src:    "OTW",
		Sid:    sid,
		OnD:    onD,
		PhoIDs: phoIDs,
		Board:  "OTW",
		OrgId:  fmt.Sprintf("org_%d", g.rand.Intn(1000)),
		Images: images,
	}
}

// GenerateCLGProperty generates a test CLG property
func (g *TestDataGenerator) GenerateCLGProperty(id, sid string, imageCount int) *TestProperty {
	onD := 20250101 + g.rand.Intn(365) // Random date in 2025

	phoIDs := make([]string, imageCount)
	images := make([]TestImage, imageCount)

	for i := 0; i < imageCount; i++ {
		phoIDs[i] = fmt.Sprintf("%d", i)

		// Calculate mui_last34 and mui_last12 from sid
		mui_last34 := sid[len(sid)-2:]
		mui_last12 := sid[len(sid)-2:]

		relativePath := fmt.Sprintf("oreb/mls/%s/%s/%s_%d.jpg",
			mui_last34, mui_last12, sid, i)

		images[i] = TestImage{
			RelativePath: relativePath,
		}
	}

	return &TestProperty{
		ID:     id,
		Src:    "CLG",
		Sid:    sid,
		OnD:    onD,
		PhoIDs: phoIDs,
		Board:  "CLG",
		OrgId:  fmt.Sprintf("org_%d", g.rand.Intn(1000)),
		Images: images,
	}
}

// GenerateRandomProperty generates a random property of any supported type
func (g *TestDataGenerator) GenerateRandomProperty(id string) *TestProperty {
	sources := []string{"TRB", "DDF", "OTW", "CLG"}
	src := sources[g.rand.Intn(len(sources))]

	// Generate random SID
	sid := fmt.Sprintf("%d", 1000000+g.rand.Intn(8999999))

	// Random image count (1-5)
	imageCount := 1 + g.rand.Intn(5)

	switch src {
	case "TRB":
		return g.GenerateTRBProperty(id, sid, imageCount)
	case "DDF":
		return g.GenerateDDFProperty(id, sid, imageCount)
	case "OTW":
		return g.GenerateOTWProperty(id, sid, imageCount)
	case "CLG":
		return g.GenerateCLGProperty(id, sid, imageCount)
	default:
		return g.GenerateTRBProperty(id, sid, imageCount)
	}
}

// GenerateTestBatch generates a batch of test properties
func (g *TestDataGenerator) GenerateTestBatch(count int) []*TestProperty {
	properties := make([]*TestProperty, count)

	for i := 0; i < count; i++ {
		id := fmt.Sprintf("test_batch_%d_%d", time.Now().Unix(), i)
		properties[i] = g.GenerateRandomProperty(id)
	}

	return properties
}

// GeneratePropertyWithExistingPhoLH generates a property that already has phoLH (should be skipped)
func (g *TestDataGenerator) GeneratePropertyWithExistingPhoLH(id, sid string) bson.M {
	onD := 20250101 + g.rand.Intn(365)

	return bson.M{
		"_id":   id,
		"src":   "TRB",
		"sid":   sid,
		"onD":   onD,
		"pho":   2,
		"board": "TRB",
		"orgId": fmt.Sprintf("org_%d", g.rand.Intn(1000)),
		"ts":    time.Now(),
		"mt":    time.Now(),
		"phoLH": []int32{12345, 67890}, // Already migrated
		"tnLH":  int32(11111),
		"phoP":  "/1234/abcd",
	}
}

// GenerateCorruptedProperty generates a property with invalid data for error testing
func (g *TestDataGenerator) GenerateCorruptedProperty(id string, corruptionType string) bson.M {
	base := bson.M{
		"_id":   id,
		"ts":    time.Now(),
		"mt":    time.Now(),
		"board": "TRB",
		"orgId": "test_org",
	}

	switch corruptionType {
	case "missing_src":
		// Missing src field
		base["sid"] = "1234567"
		base["onD"] = 20250101
		base["pho"] = 1

	case "invalid_src":
		// Invalid src value
		base["src"] = "INVALID"
		base["sid"] = "1234567"
		base["onD"] = 20250101
		base["pho"] = 1

	case "missing_sid":
		// Missing sid field
		base["src"] = "TRB"
		base["onD"] = 20250101
		base["pho"] = 1

	case "invalid_pho":
		// Invalid pho field for TRB
		base["src"] = "TRB"
		base["sid"] = "1234567"
		base["onD"] = 20250101
		base["pho"] = "invalid"

	case "missing_phoIDs":
		// Missing phoIDs for OTW
		base["src"] = "OTW"
		base["sid"] = "1234567"
		base["onD"] = 20250101

	case "invalid_onD":
		// Invalid onD field
		base["src"] = "TRB"
		base["sid"] = "1234567"
		base["onD"] = "invalid"
		base["pho"] = 1

	default:
		// Default corruption: missing all required fields
		// Only has _id, ts, mt
	}

	return base
}

// GenerateStressTestData generates data for stress testing
func (g *TestDataGenerator) GenerateStressTestData(propertyCount, maxImagesPerProperty int) []*TestProperty {
	properties := make([]*TestProperty, propertyCount)

	for i := 0; i < propertyCount; i++ {
		id := fmt.Sprintf("stress_test_%d_%d", time.Now().Unix(), i)
		imageCount := 1 + g.rand.Intn(maxImagesPerProperty)
		properties[i] = g.GenerateRandomProperty(id)

		// Override image count
		switch properties[i].Src {
		case "TRB", "DDF":
			properties[i].Pho = imageCount
		case "OTW", "CLG":
			// Regenerate phoIDs with correct count
			phoIDs := make([]string, imageCount)
			for j := 0; j < imageCount; j++ {
				phoIDs[j] = fmt.Sprintf("%d", j)
			}
			properties[i].PhoIDs = phoIDs
		}

		// Regenerate images with correct count
		images := make([]TestImage, imageCount)
		for j := 0; j < imageCount; j++ {
			images[j] = TestImage{
				RelativePath: fmt.Sprintf("stress/%s/%s_%d.jpg", properties[i].Src, properties[i].Sid, j),
			}
		}
		properties[i].Images = images
	}

	return properties
}
