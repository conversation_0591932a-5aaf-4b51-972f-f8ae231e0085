# 需求 [fix_retry]

## 反馈

1. 几个房源没有图片

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-08-11

## 原因
1. 都是遇到这个错误  "2025-08-25T12:27:26.953-04:00 level=ERROR msg=Failed to process queue item, propId="TRBC12362690", error={}, errorMsg="processing failed: prop processing failed: failed to download and cache files: failed to download file https://trreb-image.ampre.ca/N14GHCnNsV82F-BejaQZr0Mu3laAZ0PEsbX3aYaZ6fI/rs:fit:3840:3840/L3RycmViL2xpc3RpbmdzLzQxLzk3LzEzLzE4L3AvZGQ4YWFhNzgtYzM1NC00ODc1LTk5MDMtNmY3YmQxNTMzYjEyLmpwZw.jpg: failed to read body after 3 attempts: unexpected EOF", errorType="*fmt.wrapError", file="processingQueue.go:273", workerID=27"
下载失败，要有个机制，根据失败次数，把间隔拉长。直到比如12个小时，就不再继续拉长了。然后如果尝试了3天也没有修正，就从queue里面去掉。需要类似这样的机制。

## 解决办法
1. 如果下载失败需要按照[5min,10min,15min,30min,1h，4h，10h，24h，48h，72h]进行重新下载，如果仍然失败，从queue中删除


## 是否需要补充UT

1. 已补充

## 确认日期:    2025-08-25

## online-step
1. 重启goresodownload

