package goresodownload

import (
	"context"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	// Media categories
	MediaCategoryPhoto = "Photo"
)

// getFileExtensionFromMimeType returns the appropriate file extension for a given MIME type
func getFileExtensionFromMimeType(mimeType string) string {
	switch mimeType {
	// Image types
	case "image/jpeg", "immge/jpeg", "jpeg": // Handle typo "immge/jpeg"
		return ".jpg"
	case "image/png":
		return ".png"
	case "image/gif":
		return ".gif"
	case "image/webp":
		return ".webp"
	case "image/bmp":
		return ".bmp"
	case "image/tiff":
		return ".tiff"
	case "image/svg+xml":
		return ".svg"
	case "image/heic":
		return ".heic"
	case "image/vnd.microsoft.icon":
		return ".ico"

	// Document types
	case "application/pdf":
		return ".pdf"
	case "application/msword":
		return ".doc"
	case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		return ".docx"
	case "application/vnd.ms-excel":
		return ".xls"
	case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
		return ".xlsx"
	case "application/rtf", "text/rtf":
		return ".rtf"
	case "application/vnd.oasis.opendocument.text":
		return ".odt"
	case "application/x-tika-msoffice":
		return ".doc" // Generic MS Office
	case "application/x-tika-ooxml":
		return ".docx" // Generic OOXML

	// Text types
	case "text/plain":
		return ".txt"
	case "text/html":
		return ".html"
	case "text/xml":
		return ".xml"

	// Audio types
	case "audio/mpeg":
		return ".mp3"

	// Video types
	case "video/mp4":
		return ".mp4"
	case "video/quicktime":
		return ".mov"

	// Archive types
	case "application/zip":
		return ".zip"

	// Binary/Other types
	case "application/octet-stream":
		return ".bin"
	case "application/x-dosexec":
		return ".exe"
	case "application/x-bplist":
		return ".plist"
	case "multipart/appledouble":
		return ".appledouble"
	case "url":
		return ".url"

	// Default cases
	default:
		return ".jpg" // Default to JPG for unknown types
	}
}

var BoardWatchTable = map[string]string{
	"CAR": "mls_car_raw_records",
	"DDF": "reso_crea_raw",
	"BRE": "bridge_bcre_raw",
	"EDM": "mls_rae_raw_records",
	"TRB": "reso_treb_evow_merged",
}

var BoardMergedTable = map[string]string{
	"CAR": "mls_car_master_records",
	"DDF": "reso_crea_merged",
	"BRE": "bridge_bcre_merged",
	"EDM": "mls_rae_master_records",
	"TRB": "reso_treb_evow_merged",
	"OTW": "mls_oreb_master_records",
	"CLG": "mls_creb_master_records",
}

// MediaTask represents a download task
type MediaTask struct {
	// TaskID   string
	Sid      string
	MediaKey string
	URL      string
	Type     string // photo/pdf/zip
	DestPath string
	IsPhoto  bool
	OldTnLH  int32
}

// DeleteTask represents a file deletion task
type DeleteTask struct {
	Sid      string
	MediaKey string
	Path     string
}

// MediaInfo represents media information extracted from change document
type MediaInfo struct {
	ID           string
	Sid          string
	NewMediaList []bson.M
	Timestamp    time.Time
	OldPhoLHList []int32
	OldDocLHList []string
	OldTnLH      int32
	NewFirstPic  MediaTask
}

// MediaTaskInfo represents information needed to generate media tasks
type MediaTaskInfo struct {
	Sid          string
	NewMedia     []bson.M
	OldPhoLHList []int32
	OldDocLHList []string
	Timestamp    time.Time
	Board        string
}

// AnalysisResult represents the result of media analysis
type AnalysisResult struct {
	ID            string
	Sid           string
	DownloadTasks []MediaTask
	DeleteTasks   []DeleteTask
	PhoLH         []int32  // photo hash list
	DocLH         []string // document hash list
	PropTs        time.Time
	OldTnLH       int32
	NewFirstPic   MediaTask
	// 图片下载统计字段
	MaxPicSz int64     // 下载的图片当中最大size
	AvgPicSz int64     // 所有图片平均size
	TotPicSz int64     // 总图片size
	PhodlNum int       // 下载图片数量
	PhoDl    time.Time // 下载完成时间戳
}

// MediaItemInfo represents extracted information from a media item
type MediaItemInfo struct {
	MediaKey string
	URL      string
	Type     string
	IsPhoto  bool
	Category string
}

// MediaDiffAnalyzer analyzes differences between old and new media lists
type MediaDiffAnalyzer struct {
}

// NewMediaDiffAnalyzer creates a new MediaDiffAnalyzer instance
func NewMediaDiffAnalyzer() *MediaDiffAnalyzer {
	return &MediaDiffAnalyzer{}
}

// ensureCatFieldForTRB ensures that TRB media item has "cat" field set based on "tp" field
// If "cat" field is missing or empty, and "tp" starts with "image/" or contains "jpeg",
// sets "cat" to "Photo". This is only applicable to TRB board.
func (a *MediaDiffAnalyzer) ensureCatFieldForTRB(media bson.M) {
	// Check if "cat" field already exists and is not empty
	if cat, ok := media["cat"].(string); ok && cat != "" {
		return // "cat" field already exists and is not empty
	}

	// Check "tp" field to determine if this should be categorized as "Photo"
	if tp, ok := media["tp"].(string); ok {
		if strings.HasPrefix(tp, "image/") || strings.Contains(strings.ToLower(tp), "jpeg") {
			media["cat"] = "Photo"
		}
	}
}

// processMediaList processes media list and adds hash for each media item
// For TRB board, also ensures "cat" field is set based on "tp" field
func (a *MediaDiffAnalyzer) processMediaList(mediaList []bson.M, isTRB bool) {
	for _, mediaMap := range mediaList {
		if isTRB {
			a.ensureCatFieldForTRB(mediaMap)
		}
		hash := a.generateHashForOneMedia(mediaMap)
		mediaMap["hash"] = hash
	}
}

// Analyze compares old and new media lists and generates download and delete tasks
func (a *MediaDiffAnalyzer) Analyze(newProp bson.M, board string) (AnalysisResult, error) {
	golog.Debug("Analyze0", "board", board)
	// For regular boards, validate board type
	if _, exists := BoardMergedTable[board]; !exists {
		golog.Error("Invalid board", "board", board)
		return AnalysisResult{}, fmt.Errorf("invalid board: %s", board)
	}

	// 1. Extract new media information
	mediaInfo, err := a.getNewMediaInfo(newProp, board)
	golog.Debug("getNewMediaInfo return", "mediaInfo", mediaInfo, "err", err)
	if err != nil {
		golog.Error("Failed to get regular media info",
			"newProp", newProp,
			"error", err.Error())
		return AnalysisResult{}, err
	}
	golog.Debug("Analyze1", "mediaInfo", mediaInfo)

	// 2. Get old media from merged table
	var oldPhoLHList []int32
	var oldDocLHList []string
	var oldTnLH int32
	if board == "TRB" {
		// For TRB board, we already have old media lists from fullDocument
		oldPhoLHList = mediaInfo.OldPhoLHList
		oldDocLHList = mediaInfo.OldDocLHList
		oldTnLH = mediaInfo.OldTnLH
		golog.Debug("Analyze2", "oldTnLH", oldTnLH)
	} else {
		// For other boards, get old media lists from merged table
		oldPhoLHList, oldDocLHList, oldTnLH, err = a.getOldLHListFromMergedTable(mediaInfo.ID, board)
		if err != nil {
			golog.Error("Failed to get old media from merged table",
				"sid", mediaInfo.Sid,
				"error", err)
			return AnalysisResult{}, err
		}
	}
	golog.Debug("getOldLHListFromMergedTable return", "oldPhoLHList", oldPhoLHList, "oldDocLHList", oldDocLHList, "err", err)

	// 3. Prepare tasks
	taskInfo := &MediaTaskInfo{
		Sid:          mediaInfo.Sid,
		NewMedia:     mediaInfo.NewMediaList,
		OldPhoLHList: oldPhoLHList,
		OldDocLHList: oldDocLHList,
		Timestamp:    mediaInfo.Timestamp,
		Board:        board,
	}
	downloadTasks, deleteTasks, err := a.prepareMediaTasks(taskInfo)
	if err != nil {
		return AnalysisResult{}, err
	}
	golog.Debug("Analyze3", "downloadTasks", downloadTasks, "deleteTasks", deleteTasks, "err", err)

	// 4. Generate hash lists
	phoLH, docLH := a.generateHashList(mediaInfo.NewMediaList)
	golog.Debug("Analyze4", "phoLH", phoLH, "docLH", docLH)

	golog.Info("Media analysis complete",
		"_id", mediaInfo.ID,
		"sid", mediaInfo.Sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH),
		"propTs", mediaInfo.Timestamp,
	)

	return AnalysisResult{
		ID:            mediaInfo.ID,
		Sid:           mediaInfo.Sid,
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
		PropTs:        mediaInfo.Timestamp,
		OldTnLH:       oldTnLH,
		NewFirstPic:   mediaInfo.NewFirstPic,
	}, nil
}

// getNewMediaInfo extracts media information from change document
// input: newProp is a new property, board is the board name, {ListingId: "123", _id: "123", Media: [{key: "123", MediaCategory: "Photo",url: "http://example.com/123.jpg"}]}
// output: a MediaInfo, {ID: "123", Sid: "123", NewMediaList: [{key: "123", MediaCategory: "Photo",url: "http://example.com/123.jpg", hash: 123}], Timestamp: time.Time}
func (a *MediaDiffAnalyzer) getNewMediaInfo(newProp bson.M, board string) (*MediaInfo, error) {
	var sid string
	var _id string
	var tnLH int32
	var sidField string
	var oldPhoLHList []int32
	var oldDocLHList []string
	if board == "TRB" || board == "DDF" {
		sidField = "ListingKey"
	} else {
		sidField = "ListingId"
	}
	if key, ok := newProp[sidField].(string); ok {
		sid = key
		golog.Debug("getNewMediaInfo", "sid", sid)
	}
	if key, ok := newProp["_id"].(string); ok {
		_id = key
		golog.Debug("getNewMediaInfo", "_id", _id)
	}
	if board == "TRB" {
		// Handle both primitive.A and []int32 types
		phoLHRaw := newProp["phoLH"]
		if phoLHRaw == nil {
			golog.Debug("old phoLH is nil", "id", _id)
		} else {
			switch v := newProp["phoLH"].(type) {
			case primitive.A:
				for _, val := range v {
					switch vv := val.(type) {
					case int:
						oldPhoLHList = append(oldPhoLHList, int32(vv))
					case int32:
						oldPhoLHList = append(oldPhoLHList, vv)
					case int64:
						oldPhoLHList = append(oldPhoLHList, int32(vv))
					case float64:
						oldPhoLHList = append(oldPhoLHList, int32(vv))
					default:
						golog.Warn("skip invalid phoLH element",
							"value", vv,
							"type", fmt.Sprintf("%T", vv))
					}
				}
			case []int32:
				oldPhoLHList = append(oldPhoLHList, v...)
			default:
				golog.Warn("phoLH unknown type:", "type", fmt.Sprintf("%T", v), "id", _id, "phoLH", phoLHRaw, "board", board)
			}
		}

		// Handle both primitive.A and []string types for docLH
		docLHRaw := newProp["docLH"]
		if docLHRaw == nil {
			golog.Debug("old docLH is nil", "id", _id)
		} else {
			switch v := newProp["docLH"].(type) {
			case primitive.A:
				for _, val := range v {
					oldDocLHList = append(oldDocLHList, val.(string))
				}
			case []string:
				oldDocLHList = append(oldDocLHList, v...)
			default:
				golog.Warn("docLH unknown type:", "type", fmt.Sprintf("%T", v), "id", _id, "docLH", docLHRaw, "board", board)
			}
		}
		// get tnLH
		tnLHRaw := newProp["tnLH"]
		if tnLHRaw == nil {
			golog.Debug("tnLH is nil, using default value 0", "id", _id)
			tnLH = 0
		} else {
			switch v := tnLHRaw.(type) {
			case int32:
				tnLH = v
			case int:
				tnLH = int32(v)
			case int64:
				tnLH = int32(v)
			case float64:
				tnLH = int32(v)
			default:
				golog.Warn("Unexpected type for tnLH:", "type", fmt.Sprintf("%T", v), "id", _id, "tnLH", tnLHRaw, "board", board)
				tnLH = 0
			}
		}
	}

	if sid == "" || _id == "" {
		return nil, fmt.Errorf("invalid listingId or _id field format in newProp")
	}

	// Get new media from fullDocument
	var mediaField interface{}
	if board == "TRB" {
		mediaField = newProp["media"]
		if mediaField == nil {
			golog.Warn("no media field in fullDocument", "id", _id)
		}
	} else {
		mediaField = newProp["Media"]
		if mediaField == nil {
			golog.Warn("no Media field in fullDocument", "id", _id)
		}
	}

	// Convert media field to []bson.M
	var newMediaList []bson.M
	var newPhotos []bson.M
	if mediaField != nil {
		isTRB := board == "TRB"
		switch v := mediaField.(type) {
		case []bson.M:
			newMediaList = v
			a.processMediaList(newMediaList, isTRB)
		case []interface{}:
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					newMediaList = append(newMediaList, mediaMap)
				}
			}
			a.processMediaList(newMediaList, isTRB)
		case primitive.A:
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					newMediaList = append(newMediaList, mediaMap)
				}
			}
			a.processMediaList(newMediaList, isTRB)
		default:
			golog.Error("Invalid media field type",
				"type", fmt.Sprintf("%T", mediaField))
			return nil, fmt.Errorf("invalid media field format in newProp")
		}
	}
	newPhotos = newMediaList
	var filteredPhotos []bson.M
	var filteredDocs []bson.M
	if board == "TRB" {
		filteredPhotos, filteredDocs = FilterMedias(newMediaList)
		newMediaList = append(filteredPhotos, filteredDocs...)
		newPhotos = filteredPhotos
	}

	golog.Debug("getNewMediaInfo", "newMediaList", newMediaList)

	// 使用新的PropTs获取逻辑，保证与文件路径计算的一致性
	ts, err := GetPropTsForPath(newProp, board)
	if err != nil {
		golog.Error("Failed to get PropTs for path",
			"_id", _id, "board", board, "error", err)
		return nil, fmt.Errorf("failed to get PropTs for path: %w", err)
	}

	// 先检查是否已经有phoP字段，如果有则跳过生成
	var filePath string
	if !ShouldSkipPhoPGeneration(newProp) {
		// Get full file path '/L1/L2'
		filePath, err = levelStore.GetFullFilePathForProp(ts, board, sid)
		if err != nil {
			golog.Error("Failed to get full file path",
				"sid", sid,
				"error", err)
			return nil, err
		}
	} else {
		// 如果已有phoP字段，直接使用它
		if phoP, ok := newProp["phoP"].(string); ok {
			filePath = phoP
		}
	}

	var newFirstPic MediaTask
	downloadTasks := a.generateDownloadTasks(newPhotos, sid, filePath)
	if len(downloadTasks) > 0 {
		newFirstPic = downloadTasks[0]
	}
	golog.Debug("getNewMediaInfo", "ts", ts)

	return &MediaInfo{
		ID:           _id,
		Sid:          sid,
		NewMediaList: newMediaList,
		Timestamp:    ts, // 使用GetPropTsForPath获取的时间戳，保证与文件路径计算的一致性
		OldPhoLHList: oldPhoLHList,
		OldDocLHList: oldDocLHList,
		OldTnLH:      tnLH,
		NewFirstPic:  newFirstPic,
	}, nil
}

// getOldLHListFromMergedTable gets the old media list from merged table
// input: id is the listing id, board is the board name
// output: a list of photo hashes, a list of document hashes, error
func (a *MediaDiffAnalyzer) getOldLHListFromMergedTable(id string, board string) ([]int32, []string, int32, error) {
	// get merged collection
	collName := BoardMergedTable[board]
	if collName == "" {
		return nil, nil, 0, fmt.Errorf("failed to get merged collection for board: %s", board)
	}
	mergedCol := gomongo.Coll("rni", collName)

	query := bson.M{"_id": id}
	projection := bson.M{"phoLH": 1, "docLH": 1}
	ctx := context.Background()

	var oldDoc bson.M
	err := mergedCol.FindOne(ctx, query, projection).Decode(&oldDoc)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No old media found in merged table",
				"_id", id)
			return []int32{}, []string{}, 0, nil
		}
		return nil, nil, 0, fmt.Errorf("failed to get old media from merged table: %w", err)
	}

	golog.Debug("Found old document",
		"_id", id,
		"board", board,
		"doc", oldDoc)

	phoLHList := make([]int32, 0)
	docLHList := make([]string, 0)
	if phoLH, ok := oldDoc["phoLH"].(primitive.A); ok {
		for _, v := range phoLH {
			phoLHList = append(phoLHList, int32(v.(int32)))
		}
	}
	if docLH, ok := oldDoc["docLH"].(primitive.A); ok {
		for _, v := range docLH {
			docLHList = append(docLHList, v.(string))
		}
	}
	tnLH := int32(0)
	if tn, ok := oldDoc["tnLH"].(int32); ok {
		tnLH = tn
	}
	return phoLHList, docLHList, tnLH, nil
}

// prepareMediaTasks prepares media tasks for regular boards
// input: info is a MediaTaskInfo, {Sid: "123", NewMedia: [{key: "123", MediaCategory: "Photo",url: "http://example.com/123.jpg", hash: 123}], OldPhoLHList: [123, 456], OldDocLHList: ["789.pdf", "101.pdf"], Timestamp: time.Time, Board: "CAR"}
// output: a list of download tasks, a list of delete tasks, error
func (a *MediaDiffAnalyzer) prepareMediaTasks(info *MediaTaskInfo) ([]MediaTask, []DeleteTask, error) {
	// build maps for comparison
	oldPhotoMap := make(map[int32]bool)
	for _, hash := range info.OldPhoLHList {
		oldPhotoMap[hash] = true
	}

	oldDocMap := make(map[int32]string) // map[hash]extension
	for _, docHash := range info.OldDocLHList {
		// Extract hash and extension
		parts := strings.Split(docHash, ".")
		if len(parts) != 2 {
			golog.Error("Invalid document hash format",
				"docHash", docHash)
			return nil, nil, fmt.Errorf("invalid document hash format: %s", docHash)
		}
		hash, err := strconv.Atoi(parts[0])
		if err != nil {
			golog.Error("Failed to convert hash to int",
				"docHash", docHash,
				"error", err)
			return nil, nil, fmt.Errorf("failed to convert hash to int: %w", err)
		}
		oldDocMap[int32(hash)] = "." + parts[1]
	}

	var (
		toDownload []bson.M
		toDelete   []struct {
			hash    int32
			isPhoto bool
			ext     string
		}
	)

	// find media to download and delete
	for _, m := range info.NewMedia {
		hash := a.generateHashForOneMedia(m)
		if hash == 0 {
			golog.Error("Invalid hash in media",
				"media", m)
			continue
		}

		// Check if it's a photo
		isPhoto := false
		if cat, ok := m["MediaCategory"].(string); ok {
			isPhoto = strings.Contains(strings.ToLower(cat), "photo")
		} else if cat, ok := m["cat"].(string); ok {
			isPhoto = cat == "Photo"
		}

		if isPhoto {
			// Handle photo
			if !oldPhotoMap[hash] {
				toDownload = append(toDownload, m) // new photo to download
			}
		} else {
			// Handle document
			if _, exists := oldDocMap[hash]; !exists {
				toDownload = append(toDownload, m) // new document to download
			}
		}
	}

	// find photos to delete
	for _, hash := range info.OldPhoLHList {
		found := false
		for _, m := range info.NewMedia {
			mediaHash := a.generateHashForOneMedia(m)
			if mediaHash == hash {
				found = true
				break
			}
		}
		if !found {
			toDelete = append(toDelete, struct {
				hash    int32
				isPhoto bool
				ext     string
			}{hash: hash, isPhoto: true, ext: ".jpg"})
		}
	}

	// find documents to delete
	for hash, ext := range oldDocMap {
		found := false
		for _, m := range info.NewMedia {
			mediaHash := a.generateHashForOneMedia(m)
			if mediaHash == hash {
				found = true
				break
			}
		}
		if !found {
			toDelete = append(toDelete, struct {
				hash    int32
				isPhoto bool
				ext     string
			}{hash: hash, isPhoto: false, ext: ext})
		}
	}

	// Get full file path
	filePath, err := levelStore.GetFullFilePathForProp(info.Timestamp, info.Board, info.Sid)
	if err != nil {
		golog.Error("Failed to get full file path",
			"sid", info.Sid,
			"error", err)
		return nil, nil, err
	}

	golog.Debug("Media comparison results",
		"toDownload", len(toDownload),
		"toDelete", len(toDelete))

	// Generate download tasks
	downloadTasks := a.generateDownloadTasks(toDownload, info.Sid, filePath)

	// Generate delete tasks
	deleteTasks := a.generateDeleteTasks(toDelete, info.Sid, filePath)

	return downloadTasks, deleteTasks, nil
}

// generateDownloadTasks generates download tasks for regular boards
func (a *MediaDiffAnalyzer) generateDownloadTasks(toDownload []bson.M, sid string, filePath string) []MediaTask {
	var downloadTasks []MediaTask
	for _, m := range toDownload {
		// Check if it's a TRB board media by looking for 'key' field
		isTreb := false
		if _, ok := m["key"]; ok {
			isTreb = true
		}
		info, err := a.extractMediaInfo(m, isTreb)
		if err != nil {
			golog.Error("Failed to extract media info",
				"sid", sid,
				"error", err)
			continue
		}
		fileName, err := a.generateFileName(sid, m, info.Type)
		if err != nil {
			golog.Error("Failed to generate file name",
				"sid", sid,
				"error", err)
			continue
		}

		task := MediaTask{
			Sid:      sid,
			MediaKey: info.MediaKey,
			URL:      info.URL,
			Type:     info.Type,
			IsPhoto:  info.IsPhoto,
			DestPath: filepath.Join(filePath, fileName),
		}
		downloadTasks = append(downloadTasks, task)
		golog.Debug("New media found for download",
			"sid", sid,
			"mediaKey", info.MediaKey,
			"order", m["Order"],
			"type", info.Type)
	}
	return downloadTasks
}

// generateDeleteTasks generates delete tasks for regular boards
func (a *MediaDiffAnalyzer) generateDeleteTasks(toDelete []struct {
	hash    int32
	isPhoto bool
	ext     string
}, sid string, filePath string) []DeleteTask {
	var deleteTasks []DeleteTask
	for _, item := range toDelete {
		base62, err := levelStore.Int32ToBase62(item.hash)
		if err != nil {
			golog.Error("Failed to convert hash to base62",
				"hash", item.hash,
				"error", err)
			// Skip this item if hash conversion fails
			continue
		}

		// Only create task if we have a valid base62 string
		if base62 == "" {
			golog.Error("Empty base62 string",
				"hash", item.hash)
			continue
		}

		fileName := fmt.Sprintf("%s_%s%s", sid, base62, item.ext)
		task := DeleteTask{
			Sid:  sid,
			Path: filepath.Join(filePath, fileName),
		}
		deleteTasks = append(deleteTasks, task)
		golog.Debug("Media found for deletion",
			"sid", sid,
			"hash", item.hash,
			"isPhoto", item.isPhoto,
			"ext", item.ext,
			"fileName", fileName)
	}
	return deleteTasks
}

// generateFileName generates a file name for a media item
func (a *MediaDiffAnalyzer) generateFileName(sid string, media bson.M, tp string) (string, error) {
	if media == nil {
		return "", fmt.Errorf("media info is nil")
	}

	// Get media key
	var mediaKey string
	if key, ok := media["MediaKey"].(string); ok {
		mediaKey = key
	} else if key, ok := media["key"].(string); ok {
		mediaKey = key
	}
	if mediaKey == "" {
		return "", fmt.Errorf("missing media key")
	}

	// Get URL for file extension
	var url string
	if u, ok := media["MediaURL"].(string); ok {
		url = u
	} else if u, ok := media["url"].(string); ok {
		url = u
	}
	if url == "" {
		return "", fmt.Errorf("missing URL")
	}

	// Get file extension from media type
	ext := getFileExtensionFromMimeType(tp)

	// all photo files should save as jpg
	if strings.HasPrefix(tp, "image/") || strings.Contains(strings.ToLower(tp), "jpeg") {
		ext = ".jpg"
	}

	// Generate hash for filename
	hash := levelStore.MurmurToInt32(mediaKey)
	fileName, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return "", err
	}
	fileName = fmt.Sprintf("%s_%s%s", sid, fileName, ext)

	return fileName, nil
}

// extractMediaInfo extracts common media information from a media item
// input: media is a media item, isTreb is true if the media is from TREB
// output: a MediaItemInfo, {MediaKey: "123", URL: "http://example.com/123.jpg", Type: "image/jpeg", Category: "Photo", IsPhoto: true}
func (a *MediaDiffAnalyzer) extractMediaInfo(media bson.M, isTreb bool) (*MediaItemInfo, error) {
	if media == nil {
		return nil, fmt.Errorf("media info is nil")
	}

	info := &MediaItemInfo{}

	// Get media key
	if isTreb {
		if key, ok := media["key"].(string); ok {
			info.MediaKey = key
		}
	} else {
		if key, ok := media["MediaKey"].(string); ok {
			info.MediaKey = key
		}
	}
	if info.MediaKey == "" {
		return nil, fmt.Errorf("missing media key")
	}

	// Get URL
	if isTreb {
		if u, ok := media["url"].(string); ok {
			info.URL = u
		}
	} else {
		// Try different URL field names
		if u, ok := media["MediaURL"].(string); ok {
			info.URL = u
		}
	}
	if info.URL == "" {
		return nil, fmt.Errorf("missing URL")
	}

	// Get media type
	if isTreb {
		if t, ok := media["tp"].(string); ok {
			info.Type = t
		}
	} else {
		if t, ok := media["MimeType"].(string); ok {
			info.Type = t
		}
	}
	if info.Type == "" {
		golog.Error("Missing media type",
			"sid", info.MediaKey,
			"media", media)
		info.Type = "image/jpeg"
	}

	// Get media category
	if isTreb {
		if t, ok := media["cat"].(string); ok && t != "" {
			info.Category = t
		} else {
			// If no cat value or empty cat value, determine category based on tp field
			if tp, ok := media["tp"].(string); ok {
				if strings.HasPrefix(tp, "image/") || strings.Contains(strings.ToLower(tp), "jpeg") {
					info.Category = "Photo"
				} else {
					info.Category = "other"
				}
			} else {
				info.Category = "other"
			}
		}
	} else {
		if t, ok := media["MediaCategory"].(string); ok {
			info.Category = t
		}
	}
	if info.Category == "" {
		golog.Error("Missing media category",
			"sid", info.MediaKey,
			"media", media)
	}
	info.IsPhoto = strings.Contains(strings.ToLower(info.Category), "photo")

	return info, nil
}

// generateHashList generates a list of hashes from media items
// input: medias is a list of media items, [{key: "123", MediaCategory: "Photo",url: "http://example.com/123.jpg"}, {key: "456", MediaCategory: "Document",url: "http://example.com/456.pdf"}]
// output: a list of photo hashes, a list of document hashes, [123, 456], [222.pdf, 333.pdf]
func (a *MediaDiffAnalyzer) generateHashList(medias []bson.M) ([]int32, []string) {
	var photoHashList []int32
	var docHashList []string
	if len(medias) == 0 {
		return []int32{}, []string{} // Return empty slice instead of nil
	}

	for _, m := range medias {
		hash := a.generateHashForOneMedia(m)
		if hash == 0 {
			continue // skip invalid key
		}
		// Check if it's a photo
		isPhoto := false
		if cat, ok := m["MediaCategory"].(string); ok {
			isPhoto = strings.Contains(strings.ToLower(cat), "photo")
		} else if cat, ok := m["cat"].(string); ok {
			isPhoto = cat == "Photo"
		}
		if isPhoto {
			photoHashList = append(photoHashList, hash)
		} else {
			// Get media type for extension determination
			var tp string
			if t, ok := m["MimeType"].(string); ok {
				tp = t
			} else if t, ok := m["tp"].(string); ok {
				tp = t
			}

			// Get file extension based on media type
			ext := getFileExtensionFromMimeType(tp)
			// For documents without media type, default to PDF
			if tp == "" {
				ext = ".pdf"
			}
			name := fmt.Sprintf("%d%s", hash, ext)
			docHashList = append(docHashList, name)
		}
	}
	return photoHashList, docHashList
}

// generateHashForOneMedia generates a hash for a media item
// input: media is a media item, {key: "123", MediaCategory: "Photo",url: "http://example.com/123.jpg"}
// output: a hash, 123
func (a *MediaDiffAnalyzer) generateHashForOneMedia(media bson.M) int32 {
	// Try to get key from different fields
	var keyStr string
	var ok bool

	// For TRB board, try key field first (as expected by tests)
	if keyStr, ok = media["key"].(string); !ok {
		// For other boards, try MediaKey field
		if keyStr, ok = media["MediaKey"].(string); !ok {
			// Fallback to id field
			if keyStr, ok = media["id"].(string); !ok {
				golog.Error("Invalid key",
					"media", media)
				return 0
			}
		}
	}

	hash := levelStore.MurmurToInt32(keyStr)
	return hash
}

// FilterMedias filters medias by status, permission, and statement
// input: medias is a list of media
// output: a list of filtered photo medias, a list of filtered document medias
func FilterMedias(medias []bson.M) ([]bson.M, []bson.M) {
	if len(medias) == 0 {
		return []bson.M{}, []bson.M{}
	}

	// Define size priority for photo selection
	sizePriority := []string{
		"LargestNoWatermark",
		"Largest",
		"Large",
		"media",
		"Thumbnail",
	}

	// Initialize result slices
	photoResult := make([]bson.M, 0)
	docResult := make([]bson.M, 0)

	// Group photos by ID and collect docs in order
	groupedPhotos := make(map[int][]bson.M)
	for i, media := range medias {
		// Skip invalid media
		u, ok := media["url"].(string)
		if !ok || u == "" {
			golog.Warn("Skipping invalid media, url is empty",
				"media", media)
			continue
		}
		status, ok := media["status"].(string)
		if !ok || status != "Active" {
			golog.Warn("Skipping invalid media, status is not Active",
				"media", media)
			continue
		}

		// Handle non-photo media
		tp, ok := media["tp"].(string)
		if !ok {
			golog.Warn("Skipping invalid media, tp is not image/jpeg",
				"media", media)
			continue
		}
		cat, ok := media["cat"].(string)
		if !ok {
			cat = ""
		}
		if (!strings.HasPrefix(tp, "image/")) && (!strings.Contains(strings.ToLower(tp), "jpeg")) && (cat != "Photo") {
			// Add index to preserve original order for documents
			mediaWithIndex := make(bson.M)
			for k, v := range media {
				mediaWithIndex[k] = v
			}
			mediaWithIndex["_originalIndex"] = i
			docResult = append(docResult, mediaWithIndex)
			continue
		}
		// Group photos by ID - handle both int and float64 types
		var idVal int
		switch v := media["id"].(type) {
		case int:
			idVal = v
		case int64:
			idVal = int(v)
		case int32:
			idVal = int(v)
		case float64:
			idVal = int(v)
		default:
			golog.Warn("Skipping invalid media, id is not int or float64",
				"id_type", fmt.Sprintf("%T", media["id"]),
				"media", media)
			continue
		}
		groupedPhotos[idVal] = append(groupedPhotos[idVal], media)
	}

	// Sort photo IDs numerically
	sortedIDs := make([]int, 0, len(groupedPhotos))
	for id := range groupedPhotos {
		sortedIDs = append(sortedIDs, id)
	}
	sort.Slice(sortedIDs, func(i, j int) bool {
		return sortedIDs[i] < sortedIDs[j]
	})

	// Select best photo for each ID
	for _, id := range sortedIDs {
		photos := groupedPhotos[id]
		if bestPhoto := selectBestPhoto(photos, sizePriority); bestPhoto != nil {
			photoResult = append(photoResult, *bestPhoto)
		}
	}

	// Sort documents by original index to preserve order
	sort.Slice(docResult, func(i, j int) bool {
		indexI, okI := docResult[i]["_originalIndex"].(int)
		indexJ, okJ := docResult[j]["_originalIndex"].(int)
		if !okI || !okJ {
			return false // Keep original order if index is missing
		}
		return indexI < indexJ
	})

	// Remove the temporary _originalIndex field from documents
	for i := range docResult {
		delete(docResult[i], "_originalIndex")
	}

	return photoResult, docResult
}

// selectBestPhoto selects the best photo based on size priority
func selectBestPhoto(photos []bson.M, sizePriority []string) *bson.M {
	if len(photos) == 0 {
		return nil
	}

	// If no size description, return the first photo
	sizeDesc, ok := photos[0]["sizeDesc"].(string)
	if !ok || sizeDesc == "" {
		return &photos[0]
	}

	// Try to find the highest priority size
	for _, size := range sizePriority {
		for _, photo := range photos {
			if desc, _ := photo["sizeDesc"].(string); desc == size {
				return &photo
			}
		}
	}

	// If no matching size found, return the first photo
	return &photos[0]
}
